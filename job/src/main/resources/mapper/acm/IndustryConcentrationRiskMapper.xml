<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.acm.mapper.IndustryConcentrationRiskMapper">

    <!-- 结果映射 -->
    <resultMap type="com.xl.alm.job.acm.entity.IndustryConcentrationRiskEntity" id="IndustryConcentrationRiskResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="industryName" column="industry_name"/>
        <result property="industryStatisticsFlag" column="industry_statistics_flag"/>
        <result property="bookValue" column="book_value"/>
        <result property="weightPercentage" column="weight_percentage"/>
        <result property="industryConcentration" column="industry_concentration"/>
        <result property="counterpartyRanking" column="counterparty_ranking"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 删除指定账期的行业集中度风险表数据 -->
    <delete id="deleteIndustryConcentrationRiskByPeriod" parameterType="String">
        DELETE FROM t_acm_industry_concentration_risk
        WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 批量插入行业集中度风险表数据 -->
    <insert id="batchInsertIndustryConcentrationRisk" parameterType="java.util.List">
        INSERT INTO t_acm_industry_concentration_risk
        (accounting_period, industry_name, industry_statistics_flag, book_value, weight_percentage, industry_concentration, counterparty_ranking, create_time, create_by, is_del)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.industryName}, #{item.industryStatisticsFlag}, #{item.bookValue}, #{item.weightPercentage}, #{item.industryConcentration}, #{item.counterpartyRanking}, NOW(), 'system', 0)
        </foreach>
    </insert>

    <!-- 根据资产整体明细表生成行业集中度风险表数据 -->
    <select id="generateIndustryConcentrationRisk" parameterType="String" resultMap="IndustryConcentrationRiskResult">
        WITH asset_dedup AS (
            -- 1. 按资产进行去重汇总（相同资产在不同账户下会有多条记录）
            SELECT
                accounting_period,
                security_code,
                asset_name,
                industry_statistics_flag,
                SUM(book_value) as asset_book_value
            FROM t_ast_asset_detail_overall
            WHERE accounting_period = #{accountingPeriod}
                AND industry_statistics_flag IS NOT NULL
                AND industry_statistics_flag != ''
                AND industry_statistics_flag != '不考虑'  -- 排除"不考虑"行业
                AND industry_statistics_flag != '0'      -- 排除"0"值
                AND account_name != '04'  -- 排除投连账户
                AND is_del = 0
            GROUP BY accounting_period, security_code, asset_name, industry_statistics_flag
        ),
        industry_summary AS (
            -- 2. 按行业汇总账面价值
            SELECT
                accounting_period,
                industry_statistics_flag,
                SUM(asset_book_value) as book_value
            FROM asset_dedup
            GROUP BY accounting_period, industry_statistics_flag
            HAVING SUM(asset_book_value) > 0
        ),
        total_value AS (
            -- 3. 计算所有行业账面价值总和
            SELECT SUM(book_value) as total_book_value
            FROM industry_summary
        )
        SELECT
            t1.accounting_period,
            -- 4. 将行业名称转换为字典编码存储（用于前端字典显示）
            CASE t1.industry_statistics_flag
                -- 标准行业名称映射
                WHEN '农林牧渔' THEN '01'
                WHEN '煤炭' THEN '02'
                WHEN '采掘' THEN '02'  -- 采掘归类为煤炭
                WHEN '基础化工' THEN '03'
                WHEN '化工' THEN '03'  -- 化工归类为基础化工
                WHEN '黑色金属' THEN '04'
                WHEN '钢铁' THEN '04'  -- 钢铁归类为黑色金属
                WHEN '有色金属' THEN '05'
                WHEN '电子元器件' THEN '06'
                WHEN '电子' THEN '06'  -- 电子归类为电子元器件
                WHEN '家用电器' THEN '07'
                WHEN '食品饮料' THEN '08'
                WHEN '纺织服饰' THEN '09'
                WHEN '纺织服装' THEN '09'  -- 纺织服装归类为纺织服饰
                WHEN '轻工制造' THEN '10'
                WHEN '医药生物' THEN '11'
                WHEN '医疗保健' THEN '11'  -- 医疗保健归类为医药生物
                WHEN '公共事业' THEN '12'
                WHEN '公用事业' THEN '12'  -- 公用事业归类为公共事业
                WHEN '交通运输' THEN '13'
                WHEN '房地产' THEN '14'
                WHEN '商贸零售' THEN '15'
                WHEN '商业贸易' THEN '15'  -- 商业贸易归类为商贸零售
                WHEN '餐饮旅游' THEN '16'
                WHEN '综合' THEN '17'
                WHEN '建筑材料' THEN '18'
                WHEN '建筑装饰' THEN '19'
                WHEN '电力设备' THEN '20'
                WHEN '电气设备' THEN '20'  -- 电气设备归类为电力设备
                WHEN '国防军工' THEN '21'
                WHEN '计算机' THEN '22'
                WHEN '传媒' THEN '23'
                WHEN '通信' THEN '24'
                WHEN '银行' THEN '25'
                WHEN '非银金融' THEN '26'
                WHEN '汽车' THEN '27'
                WHEN '机械设备' THEN '28'
                -- 国际行业分类
                WHEN 'Energy' THEN '29'
                WHEN 'Materials' THEN '30'
                WHEN 'Industrials' THEN '31'
                WHEN 'Consumer Discretionary' THEN '32'
                WHEN 'Consumer Staples' THEN '33'
                WHEN 'Health Care' THEN '34'
                WHEN 'Financials' THEN '35'
                WHEN 'Information Technology' THEN '36'
                WHEN 'Telecommunication Services' THEN '37'
                WHEN 'Utilities' THEN '38'
                WHEN 'Real Estate' THEN '39'
                -- 其他行业
                WHEN '社会服务' THEN '41'
                WHEN '美容护理' THEN '42'
                WHEN '石油石化' THEN '43'
                WHEN '环保' THEN '44'
                -- 特殊情况
                WHEN '不考虑' THEN '40'  -- 不考虑映射为不适用
                WHEN '0' THEN '40'  -- 0值映射为不适用
                WHEN '' THEN '40'   -- 空值映射为不适用
                ELSE '40'  -- 其他情况默认为不适用
            END as industry_name,
            -- 5. 将行业统计标识转换为字典编码存储
            CASE t1.industry_statistics_flag
                -- 标准行业名称映射
                WHEN '农林牧渔' THEN '01'
                WHEN '煤炭' THEN '02'
                WHEN '采掘' THEN '02'  -- 采掘归类为煤炭
                WHEN '基础化工' THEN '03'
                WHEN '化工' THEN '03'  -- 化工归类为基础化工
                WHEN '黑色金属' THEN '04'
                WHEN '钢铁' THEN '04'  -- 钢铁归类为黑色金属
                WHEN '有色金属' THEN '05'
                WHEN '电子元器件' THEN '06'
                WHEN '电子' THEN '06'  -- 电子归类为电子元器件
                WHEN '家用电器' THEN '07'
                WHEN '食品饮料' THEN '08'
                WHEN '纺织服饰' THEN '09'
                WHEN '纺织服装' THEN '09'  -- 纺织服装归类为纺织服饰
                WHEN '轻工制造' THEN '10'
                WHEN '医药生物' THEN '11'
                WHEN '医疗保健' THEN '11'  -- 医疗保健归类为医药生物
                WHEN '公共事业' THEN '12'
                WHEN '公用事业' THEN '12'  -- 公用事业归类为公共事业
                WHEN '交通运输' THEN '13'
                WHEN '房地产' THEN '14'
                WHEN '商贸零售' THEN '15'
                WHEN '商业贸易' THEN '15'  -- 商业贸易归类为商贸零售
                WHEN '餐饮旅游' THEN '16'
                WHEN '综合' THEN '17'
                WHEN '建筑材料' THEN '18'
                WHEN '建筑装饰' THEN '19'
                WHEN '电力设备' THEN '20'
                WHEN '电气设备' THEN '20'  -- 电气设备归类为电力设备
                WHEN '国防军工' THEN '21'
                WHEN '计算机' THEN '22'
                WHEN '传媒' THEN '23'
                WHEN '通信' THEN '24'
                WHEN '银行' THEN '25'
                WHEN '非银金融' THEN '26'
                WHEN '汽车' THEN '27'
                WHEN '机械设备' THEN '28'
                -- 国际行业分类
                WHEN 'Energy' THEN '29'
                WHEN 'Materials' THEN '30'
                WHEN 'Industrials' THEN '31'
                WHEN 'Consumer Discretionary' THEN '32'
                WHEN 'Consumer Staples' THEN '33'
                WHEN 'Health Care' THEN '34'
                WHEN 'Financials' THEN '35'
                WHEN 'Information Technology' THEN '36'
                WHEN 'Telecommunication Services' THEN '37'
                WHEN 'Utilities' THEN '38'
                WHEN 'Real Estate' THEN '39'
                -- 其他行业
                WHEN '社会服务' THEN '41'
                WHEN '美容护理' THEN '42'
                WHEN '石油石化' THEN '43'
                WHEN '环保' THEN '44'
                -- 特殊情况
                WHEN '不考虑' THEN '40'  -- 不考虑映射为不适用
                WHEN '0' THEN '40'  -- 0值映射为不适用
                WHEN '' THEN '40'   -- 空值映射为不适用
                ELSE '40'  -- 其他情况默认为不适用
            END as industry_statistics_flag,
            t1.book_value,
            -- 5. 计算权重百分比（当前行业账面价值 ÷ 所有行业账面价值总和）
            CASE
                WHEN t2.total_book_value > 0 THEN ROUND((t1.book_value / t2.total_book_value) * 100, 4)
                ELSE 0
            END as weight_percentage,
            -- 6. 计算行业集中度（权重的平方 × 10000）
            CASE
                WHEN t2.total_book_value > 0 THEN ROUND(POWER((t1.book_value / t2.total_book_value), 2) * 10000, 2)
                ELSE 0
            END as industry_concentration,
            -- 7. 按账面价值降序排名，使用ROW_NUMBER确保连续排序
            ROW_NUMBER() OVER (ORDER BY t1.book_value DESC) as counterparty_ranking
        FROM industry_summary t1
        CROSS JOIN total_value t2
        LEFT JOIN t_acm_industry_name_mapping t3
            ON t1.industry_statistics_flag = t3.industry_statistics_flag
            AND t3.accounting_period = #{accountingPeriod}
        ORDER BY t1.book_value DESC
    </select>

</mapper>
