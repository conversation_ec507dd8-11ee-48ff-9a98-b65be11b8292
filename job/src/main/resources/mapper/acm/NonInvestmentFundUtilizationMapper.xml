<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.acm.mapper.NonInvestmentFundUtilizationMapper">

    <resultMap type="com.xl.alm.job.acm.entity.NonInvestmentFundUtilizationEntity" id="NonInvestmentFundUtilizationResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="subjectName" column="subject_name"/>
        <result property="endingBalance" column="ending_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 根据账期删除非投资资金运用表数据 -->
    <delete id="deleteNonInvestmentFundUtilizationByPeriod">
        DELETE FROM t_asm_non_investment_fund_utilization 
        WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 生成非投资资金运用表数据 -->
    <select id="generateNonInvestmentFundUtilization" resultMap="NonInvestmentFundUtilizationResult">
        SELECT
            #{accountingPeriod} as accounting_period,
            subject_code,
            subject_name,
            ending_balance,
            NOW() as create_time,
            'system' as create_by,
            NOW() as update_time,
            'system' as update_by,
            0 as is_del
        FROM t_asm_subject_summary
        WHERE accounting_period = #{accountingPeriod}
          AND subject_code IN ('130301', '130302', '100201', '********')
          AND is_del = 0
        ORDER BY subject_code
    </select>

    <!-- 批量插入非投资资金运用表数据 -->
    <insert id="batchInsertNonInvestmentFundUtilization" parameterType="java.util.List">
        INSERT INTO t_asm_non_investment_fund_utilization (
            accounting_period, subject_code, subject_name, ending_balance, 
            create_time, create_by, update_time, update_by, is_del
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountingPeriod}, #{item.subjectCode}, #{item.subjectName}, #{item.endingBalance},
                #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.isDel}
            )
        </foreach>
    </insert>

</mapper>
