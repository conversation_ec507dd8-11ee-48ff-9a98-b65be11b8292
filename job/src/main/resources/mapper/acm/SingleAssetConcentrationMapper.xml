<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.acm.mapper.SingleAssetConcentrationMapper">

    <!-- 结果映射 -->
    <resultMap type="com.xl.alm.job.acm.entity.SingleAssetConcentrationEntity" id="SingleAssetConcentrationResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="almAssetName" column="alm_asset_name"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="bookValue" column="book_value"/>
        <result property="singleAssetRanking" column="single_asset_ranking"/>
        <result property="isMajorEquityInvestment" column="is_major_equity_investment"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 删除指定账期的单一资产投资集中度风险表数据 -->
    <delete id="deleteSingleAssetConcentrationByPeriod" parameterType="String">
        DELETE FROM t_acm_single_asset_concentration
        WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 批量插入单一资产投资集中度风险表数据 -->
    <insert id="batchInsertSingleAssetConcentration" parameterType="java.util.List">
        INSERT INTO t_acm_single_asset_concentration
        (accounting_period, alm_asset_name, book_balance, book_value, single_asset_ranking, is_major_equity_investment)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.almAssetName}, #{item.bookBalance}, #{item.bookValue}, #{item.singleAssetRanking}, #{item.isMajorEquityInvestment})
        </foreach>
    </insert>

    <!-- 根据资产整体明细表生成单一资产投资集中度风险表数据 -->
    <select id="generateSingleAssetConcentration" parameterType="String" resultMap="SingleAssetConcentrationResult">
        WITH asset_summary AS (
            -- 1. 筛选单一资产统计标识为"考虑"的资产
            -- 2. 按ALM资产名称分组汇总
            -- 3. 汇总账面余额和账面价值（不考虑账户区分）
            SELECT
                accounting_period,
                alm_asset_name,
                SUM(book_balance) as book_balance,
                SUM(book_value) as book_value
            FROM t_ast_asset_detail_overall
            WHERE accounting_period = #{accountingPeriod}
                AND alm_asset_name IS NOT NULL
                AND alm_asset_name != ''
                AND single_asset_statistics_flag = '02'  -- 筛选单一资产统计标识为"考虑"的资产
                AND account_name != '04'  -- 排除投连账户
                AND is_del = 0
            GROUP BY accounting_period, alm_asset_name
            HAVING SUM(book_value) > 0
        )
        SELECT
            t1.accounting_period,
            t1.alm_asset_name,
            t1.book_balance,
            t1.book_value,
            -- 4. 按账面价值使用RANK函数进行排序，存储为数字类型
            RANK() OVER (ORDER BY t1.book_value DESC) as single_asset_ranking,
            -- 5. 默认设置为"否"（不是重大股权投资）
            '0' as is_major_equity_investment
        FROM asset_summary t1
        ORDER BY t1.book_value DESC
    </select>

</mapper>
