<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.acm.mapper.FixedIncomeCreditRatingMapper">

    <!-- 结果映射 -->
    <resultMap type="com.xl.alm.job.acm.entity.FixedIncomeCreditRatingEntity" id="FixedIncomeCreditRatingResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="creditRatingCategory" column="credit_rating_category"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 删除指定账期的固定收益类投资资产信用评级表数据 -->
    <delete id="deleteFixedIncomeCreditRatingByPeriod" parameterType="String">
        DELETE FROM t_acm_fixed_income_credit_rating
        WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 批量插入固定收益类投资资产信用评级表数据 -->
    <insert id="batchInsertFixedIncomeCreditRating" parameterType="java.util.List">
        INSERT INTO t_acm_fixed_income_credit_rating
        (accounting_period, domestic_foreign, fixed_income_term_category, credit_rating_category, book_balance)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.fixedIncomeTermCategory}, #{item.creditRatingCategory}, #{item.bookBalance})
        </foreach>
    </insert>

    <!-- 根据资产整体明细表生成境内固定收益类投资资产信用评级表数据 -->
    <select id="generateDomesticFixedIncomeCreditRating" parameterType="String" resultMap="FixedIncomeCreditRatingResult">
        SELECT
            #{accountingPeriod} as accounting_period,
            '01' as domestic_foreign,
            fixed_income_term_category as fixed_income_term_category,
            -- 标准化信用评级分类：统一转换为字典代码格式
            CASE
                WHEN credit_rating_category = 'AAA' THEN '01'
                WHEN credit_rating_category = 'AA+' THEN '02'
                WHEN credit_rating_category = 'AA' THEN '03'
                WHEN credit_rating_category = '不涉及' THEN '04'
                WHEN credit_rating_category = '免评级' THEN '05'
                WHEN credit_rating_category = '无评级' THEN '06'
                WHEN credit_rating_category IN ('01', '02', '03', '04', '05', '06') THEN credit_rating_category
                ELSE '06'  -- 其他情况默认为无评级
            END as credit_rating_category,
            SUM(book_balance) as book_balance
        FROM t_ast_asset_detail_overall
        WHERE accounting_period = #{accountingPeriod}
            AND domestic_foreign = '01'
            AND fixed_income_term_category IS NOT NULL
            AND fixed_income_term_category != ''
            AND credit_rating_category IS NOT NULL
            AND credit_rating_category NOT IN ('04', '不涉及')  -- 排除不涉及的记录
            AND account_name != '04'  -- 排除投连账户
            AND is_del = 0
        GROUP BY fixed_income_term_category,
            CASE
                WHEN credit_rating_category = 'AAA' THEN '01'
                WHEN credit_rating_category = 'AA+' THEN '02'
                WHEN credit_rating_category = 'AA' THEN '03'
                WHEN credit_rating_category = '不涉及' THEN '04'
                WHEN credit_rating_category = '免评级' THEN '05'
                WHEN credit_rating_category = '无评级' THEN '06'
                WHEN credit_rating_category IN ('01', '02', '03', '04', '05', '06') THEN credit_rating_category
                ELSE '06'
            END
        HAVING SUM(book_balance) > 0  -- 只保留有余额的记录
    </select>

    <!-- 根据资产整体明细表生成境外固定收益类投资资产信用评级表数据 -->
    <select id="generateForeignFixedIncomeCreditRating" parameterType="String" resultMap="FixedIncomeCreditRatingResult">
        SELECT
            #{accountingPeriod} as accounting_period,
            '02' as domestic_foreign,
            '07' as fixed_income_term_category,  -- 境外资产统一归类为境外固收资产
            -- 标准化信用评级分类：统一转换为字典代码格式
            CASE
                WHEN credit_rating_category = 'AAA' THEN '01'
                WHEN credit_rating_category = 'AA+' THEN '02'
                WHEN credit_rating_category = 'AA' THEN '03'
                WHEN credit_rating_category = '不涉及' THEN '04'
                WHEN credit_rating_category = '免评级' THEN '05'
                WHEN credit_rating_category = '无评级' THEN '06'
                WHEN credit_rating_category IN ('01', '02', '03', '04', '05', '06') THEN credit_rating_category
                ELSE '06'  -- 其他情况默认为无评级
            END as credit_rating_category,
            SUM(book_balance) as book_balance
        FROM t_ast_asset_detail_overall
        WHERE accounting_period = #{accountingPeriod}
            AND domestic_foreign = '02'
            AND credit_rating_category IS NOT NULL
            AND credit_rating_category NOT IN ('04', '不涉及')  -- 排除不涉及的记录
            AND account_name != '04'  -- 排除投连账户
            AND is_del = 0
        GROUP BY
            CASE
                WHEN credit_rating_category = 'AAA' THEN '01'
                WHEN credit_rating_category = 'AA+' THEN '02'
                WHEN credit_rating_category = 'AA' THEN '03'
                WHEN credit_rating_category = '不涉及' THEN '04'
                WHEN credit_rating_category = '免评级' THEN '05'
                WHEN credit_rating_category = '无评级' THEN '06'
                WHEN credit_rating_category IN ('01', '02', '03', '04', '05', '06') THEN credit_rating_category
                ELSE '06'
            END
        HAVING SUM(book_balance) > 0  -- 只保留有余额的记录
    </select>

</mapper>
