<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.acm.mapper.FixedIncomeTermDistributionMapper">

    <resultMap type="com.xl.alm.job.acm.entity.FixedIncomeTermDistributionEntity" id="FixedIncomeTermDistributionResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="remainingTermFlag" column="remaining_term_flag"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 删除指定账期的固定收益类投资资产剩余期限分布表数据 -->
    <delete id="deleteFixedIncomeTermDistributionByPeriod" parameterType="String">
        DELETE FROM t_asm_fixed_income_term_dist WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 批量插入固定收益类投资资产剩余期限分布表数据 -->
    <insert id="batchInsertFixedIncomeTermDistribution" parameterType="java.util.List">
        INSERT INTO t_asm_fixed_income_term_dist
        (accounting_period, domestic_foreign, fixed_income_term_category, remaining_term_flag, book_balance)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.fixedIncomeTermCategory},
             #{item.remainingTermFlag}, #{item.bookBalance})
        </foreach>
    </insert>

    <!-- 根据资产整体明细表生成境内固定收益类投资资产剩余期限分布表数据 -->
    <select id="generateDomesticFixedIncomeTermDistribution" parameterType="String" resultMap="FixedIncomeTermDistributionResult">
        SELECT
            #{accountingPeriod} as accounting_period,
            '01' as domestic_foreign,
            fixed_income_term_category,
            CASE remaining_term_flag
                WHEN '1年及以内' THEN '01'
                WHEN '1-3年（含3年）' THEN '02'
                WHEN '3-5年(含5年)' THEN '03'
                WHEN '3-5年（含5年）' THEN '03'
                WHEN '5-7年（含7年）' THEN '04'
                WHEN '7-10年（含10年）' THEN '05'
                WHEN '10-15年（含15年）' THEN '06'
                WHEN '15年以上' THEN '07'
                WHEN '无明确期限' THEN '08'
                ELSE remaining_term_flag
            END as remaining_term_flag,
            SUM(book_balance) as book_balance
        FROM t_ast_asset_detail_overall
        WHERE accounting_period = #{accountingPeriod}
            AND domestic_foreign = '01'
            AND fixed_income_term_category IS NOT NULL
            AND fixed_income_term_category != ''
            AND remaining_term_flag IS NOT NULL
            AND remaining_term_flag != ''
            AND account_name != '04'  -- 排除投连账户
            AND is_del = 0
        GROUP BY fixed_income_term_category,
                 CASE remaining_term_flag
                     WHEN '1年及以内' THEN '01'
                     WHEN '1-3年（含3年）' THEN '02'
                     WHEN '3-5年(含5年)' THEN '03'
                     WHEN '3-5年（含5年）' THEN '03'
                     WHEN '5-7年（含7年）' THEN '04'
                     WHEN '7-10年（含10年）' THEN '05'
                     WHEN '10-15年（含15年）' THEN '06'
                     WHEN '15年以上' THEN '07'
                     WHEN '无明确期限' THEN '08'
                     ELSE remaining_term_flag
                 END
        HAVING SUM(book_balance) > 0  -- 只保留有余额的记录
    </select>

    <!-- 根据资产整体明细表生成境外固定收益类投资资产剩余期限分布表数据 -->
    <select id="generateForeignFixedIncomeTermDistribution" parameterType="String" resultMap="FixedIncomeTermDistributionResult">
        SELECT
            #{accountingPeriod} as accounting_period,
            '02' as domestic_foreign,
            COALESCE(fixed_income_term_category, '07') as fixed_income_term_category,
            CASE remaining_term_flag
                WHEN '1年及以内' THEN '01'
                WHEN '1-3年（含3年）' THEN '02'
                WHEN '3-5年(含5年)' THEN '03'
                WHEN '3-5年（含5年）' THEN '03'
                WHEN '5-7年（含7年）' THEN '04'
                WHEN '7-10年（含10年）' THEN '05'
                WHEN '10-15年（含15年）' THEN '06'
                WHEN '15年以上' THEN '07'
                WHEN '无明确期限' THEN '08'
                ELSE remaining_term_flag
            END as remaining_term_flag,
            SUM(book_balance) as book_balance
        FROM t_ast_asset_detail_overall
        WHERE accounting_period = #{accountingPeriod}
            AND domestic_foreign = '02'
            AND remaining_term_flag IS NOT NULL
            AND remaining_term_flag != ''
            AND account_name != '04'  -- 排除投连账户
            AND is_del = 0
        GROUP BY CASE remaining_term_flag
                     WHEN '1年及以内' THEN '01'
                     WHEN '1-3年（含3年）' THEN '02'
                     WHEN '3-5年(含5年)' THEN '03'
                     WHEN '3-5年（含5年）' THEN '03'
                     WHEN '5-7年（含7年）' THEN '04'
                     WHEN '7-10年（含10年）' THEN '05'
                     WHEN '10-15年（含15年）' THEN '06'
                     WHEN '15年以上' THEN '07'
                     WHEN '无明确期限' THEN '08'
                     ELSE remaining_term_flag
                 END
        HAVING SUM(book_balance) > 0  -- 只保留有余额的记录
    </select>

</mapper>
