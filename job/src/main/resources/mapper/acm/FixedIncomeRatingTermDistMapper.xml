<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.acm.mapper.FixedIncomeRatingTermDistMapper">

    <!-- 结果映射 -->
    <resultMap type="com.xl.alm.job.acm.entity.FixedIncomeRatingTermDistEntity" id="FixedIncomeRatingTermDistResult">
        <result property="id" column="id"/>
        <result property="accountingPeriod" column="accounting_period"/>
        <result property="domesticForeign" column="domestic_foreign"/>
        <result property="creditRatingCategory" column="credit_rating_category"/>
        <result property="fixedIncomeTermCategory" column="fixed_income_term_category"/>
        <result property="bookBalance" column="book_balance"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <!-- 删除指定账期的固定收益类投资资产外部评级剩余期限分布表数据 -->
    <delete id="deleteFixedIncomeRatingTermDistByPeriod" parameterType="String">
        DELETE FROM t_acm_fixed_income_rating_term_dist
        WHERE accounting_period = #{accountingPeriod}
    </delete>

    <!-- 批量插入固定收益类投资资产外部评级剩余期限分布表数据 -->
    <insert id="batchInsertFixedIncomeRatingTermDist" parameterType="java.util.List">
        INSERT INTO t_acm_fixed_income_rating_term_dist
        (accounting_period, domestic_foreign, credit_rating_category, fixed_income_term_category, book_balance)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.accountingPeriod}, #{item.domesticForeign}, #{item.creditRatingCategory}, #{item.fixedIncomeTermCategory}, #{item.bookBalance})
        </foreach>
    </insert>

    <!-- 根据资产整体明细表生成固定收益类投资资产外部评级剩余期限分布表数据 -->
    <select id="generateFixedIncomeRatingTermDist" parameterType="String" resultMap="FixedIncomeRatingTermDistResult">
        SELECT
            #{accountingPeriod} as accounting_period,
            domestic_foreign,
            -- 标准化信用评级分类：统一转换为字典代码格式
            CASE
                WHEN credit_rating_category = 'AAA' THEN '01'
                WHEN credit_rating_category = 'AA+' THEN '02'
                WHEN credit_rating_category = 'AA' THEN '03'
                WHEN credit_rating_category = '不涉及' THEN '04'
                WHEN credit_rating_category = '免评级' THEN '05'
                WHEN credit_rating_category = '无评级' THEN '06'
                WHEN credit_rating_category IN ('01', '02', '03', '04', '05', '06') THEN credit_rating_category
                ELSE '06'  -- 其他情况默认为无评级
            END as credit_rating_category,
            CASE
                WHEN remaining_term_flag = '1年及以内' THEN '01'
                WHEN remaining_term_flag = '1-3年（含3年）' THEN '02'
                WHEN remaining_term_flag = '3-5年（含5年）' THEN '03'
                WHEN remaining_term_flag = '5-7年（含7年）' THEN '04'
                WHEN remaining_term_flag = '7-10年（含10年）' THEN '05'
                WHEN remaining_term_flag = '10-15年（含15年）' THEN '06'
                WHEN remaining_term_flag = '15年以上' THEN '07'
                WHEN remaining_term_flag = '无明确期限' THEN '08'
                WHEN remaining_term_flag = '0' THEN '08'
                ELSE remaining_term_flag
            END as fixed_income_term_category,
            SUM(book_balance) as book_balance
        FROM t_ast_asset_detail_overall
        WHERE accounting_period = #{accountingPeriod}
            AND domestic_foreign IS NOT NULL
            AND domestic_foreign != ''
            AND credit_rating_category IS NOT NULL
            AND credit_rating_category NOT IN ('04', '不涉及')  -- 排除不涉及的记录
            AND remaining_term_flag IS NOT NULL
            AND account_name != '04'  -- 排除投连账户
            AND is_del = 0
        GROUP BY domestic_foreign,
            CASE
                WHEN credit_rating_category = 'AAA' THEN '01'
                WHEN credit_rating_category = 'AA+' THEN '02'
                WHEN credit_rating_category = 'AA' THEN '03'
                WHEN credit_rating_category = '不涉及' THEN '04'
                WHEN credit_rating_category = '免评级' THEN '05'
                WHEN credit_rating_category = '无评级' THEN '06'
                WHEN credit_rating_category IN ('01', '02', '03', '04', '05', '06') THEN credit_rating_category
                ELSE '06'
            END,
            CASE
                WHEN remaining_term_flag = '1年及以内' THEN '01'
                WHEN remaining_term_flag = '1-3年（含3年）' THEN '02'
                WHEN remaining_term_flag = '3-5年（含5年）' THEN '03'
                WHEN remaining_term_flag = '5-7年（含7年）' THEN '04'
                WHEN remaining_term_flag = '7-10年（含10年）' THEN '05'
                WHEN remaining_term_flag = '10-15年（含15年）' THEN '06'
                WHEN remaining_term_flag = '15年以上' THEN '07'
                WHEN remaining_term_flag = '无明确期限' THEN '08'
                WHEN remaining_term_flag = '0' THEN '08'
                ELSE remaining_term_flag
            END
        HAVING SUM(book_balance) > 0  -- 只保留有余额的记录
        ORDER BY domestic_foreign, credit_rating_category, fixed_income_term_category
    </select>

</mapper>
