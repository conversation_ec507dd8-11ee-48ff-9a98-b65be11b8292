package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 负债规模明细表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class LiabScaleDetailEntity extends BaseEntity {
    private Long id;

    /**
     * 账期，格式：YYYYMM（如202506）
     */
    private String accountingPeriod;

    /**
     * 产品精算代码，以字母开头
     */
    private String actuarialCode;

    /**
     * 产品业务代码
     */
    private String businessCode;

    /**
     * 产品全称
     */
    private String productName;

    /**
     * 长短期标识，值域：L-长期，S-短期
     */
    private String termFlag;

    /**
     * 险种主类，值域：01-长期寿险、02-长期健康险、03-长期意外险、04-短期寿险、05-短期健康险、06-短期意外险
     */
    private String insuranceMainType;

    /**
     * 险种细类，值域：01-年金险、02-两全险、04-终身寿险等
     */
    private String insuranceSubType;

    /**
     * 设计类型，值域：01-传统险、02-分红险、03-万能险、04-投连险
     */
    private String designType;

    /**
     * 合理估计负债
     */
    private BigDecimal reasonableLiability;

    /**
     * 风险边际
     */
    private BigDecimal riskMargin;

    /**
     * 剩余边际
     */
    private BigDecimal residualMargin;

    /**
     * 长期险未决赔款准备金
     */
    private BigDecimal outstandingClaimReserveL;

    /**
     * 未到期责任准备金
     */
    private BigDecimal unearnedPremiumReserve;

    /**
     * 短期险未决赔款准备金
     */
    private BigDecimal outstandingClaimReserveS;

    /**
     * 万能投连险负债规模
     */
    private BigDecimal investmentLinkedLiability;

    /**
     * 应收分保未到期责任准备金
     */
    private BigDecimal receivableUnearnedPremiumReserve;

    /**
     * 应收分保未决赔款准备金
     */
    private BigDecimal receivableOutstandingClaimReserve;

    /**
     * 应收分保寿险责任准备金
     */
    private BigDecimal receivableLifeInsuranceReserve;

    /**
     * 应收分保长期健康险责任准备金
     */
    private BigDecimal receivableLongTermHealthReserve;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
