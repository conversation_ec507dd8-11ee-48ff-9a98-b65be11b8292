package com.xl.alm.app.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 资产定义表 Entity
 *
 * <AUTHOR> Assistant
 */
@Data
public class AssetDefinitionEntity {

    /** 主键 */
    private Long id;

    /** 账期,格式YYYYMM（如202406） */
    private String accountingPeriod;

    /** 原始账户名称,存储实际的原始文本,如传统、分红、万能、投连、资本补充债等 */
    private String accountName;

    /** 资产名称 */
    private String assetName;

    /** 证券标识代码 */
    private String securityCode;

    /** 资产小小类,01:存款,02:政府债券,03:中期票据,04:债券型基金,05:公募基金固定收益类专户,06:公司债企业债,07:固定收益类保险资产管理产品,08:货币类保险资产管理产品,09:不动产债权投资计划,10:基础设施债权投资计划,11:信托计划,12:上市普通股票,13:证券投资基金,14:可转债,15:以自有资金对保险类企业的股权投资,16:不含保证条款的股权投资计划、私募股权投资基金,17:权益类和混合类保险资管产品,18:权益类信托计划,19:不动产相关金融产品,20:权益类基金专户产品,21:REITS,22:融资回购,23:活期存款,24:回购,25:负债,26:其他,27:项目资产支持计划,28:短期、超短期融资券,29:其他普通未上市股权 */
    private String assetSubSubCategory;

    /** 境内外标识,01:境内资产,02:境外资产,03:0（表示没有境内外这个属性） */
    private String domesticForeign;

    /** 信用评级,01:AA,02:AA+,03:AAA,04:不涉及,05:免评级,06:无评级 */
    private String creditRating;

    /** 所属行业分类 */
    private String industryCategory;

    /** ALM资产名称 */
    private String almAssetName;

    /** 五级分类,01:正常类,02:关注类,03:次级类,04:可疑类,05:损失类,06:不良类 */
    private String fiveLevelClassification;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 更新者 */
    private String updateBy;

    /** 是否删除，0:否，1:是 */
    private Integer isDel;
}
