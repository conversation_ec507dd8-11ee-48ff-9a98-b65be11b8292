package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * TB0001-组合持仓表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class AccountHoldingEntity extends BaseEntity {
    
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 账期,格式YYYYMM（如202406）
     */
    private String accountingPeriod;
    
    /**
     * 账户名称,如传统、分红、万能、投连等原始文本,同时作为顶层分类使用
     */
    private String accountName;
    
    /**
     * 证券标识代码,如0003956CK、173996等
     */
    private String securityCode;
    
    /**
     * 资产名称,如中国农业银行股份有限公司、23重庆04等
     */
    private String assetName;
    
    /**
     * 市值,如********.44、*********.6等
     */
    private BigDecimal marketValue;
    
    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDel = 0;
}
