package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ALMCF实际发生数本年累计表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class AlmcfActualYtdEntity extends BaseEntity {
    
    /**
     * 主键，自增长
     */
    private Long id;
    
    /**
     * 账期，格式YYYYMM
     */
    private String accountingPeriod;
    
    /**
     * 项目名称，项目中文名称
     */
    private String itemName;
    
    /**
     * 传统账户实际发生金额，等于公司整体-分红账户-万能账户-投连账户
     */
    private BigDecimal traditionalAccount;
    
    /**
     * 分红账户实际发生金额，通过映射表计算
     */
    private BigDecimal bonusAccount;
    
    /**
     * 万能账户实际发生金额，通过映射表计算
     */
    private BigDecimal universalAccount;
    
    /**
     * 投连账户实际发生金额，通过映射表计算
     */
    private BigDecimal investmentAccount;
    
    /**
     * 普通账户实际发生金额，等于公司整体-投连账户
     */
    private BigDecimal ordinaryAccount;
    
    /**
     * 公司整体，各账户金额合计，通过映射表计算
     */
    private BigDecimal totalAccount;
    
    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDel = 0;
}
