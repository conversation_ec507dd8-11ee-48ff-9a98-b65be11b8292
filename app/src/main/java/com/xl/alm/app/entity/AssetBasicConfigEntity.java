package com.xl.alm.app.entity;

import lombok.Data;

/**
 * 资产基础配置表 Entity
 *
 * <AUTHOR> Assistant
 */
@Data
public class AssetBasicConfigEntity {

    /** 主键 */
    private Long id;

    /** 账期,格式YYYYMM（如202406） */
    private String accountingPeriod;

    /** 配置序号 */
    private Integer sequenceNumber;

    /** 资产小小类,与TB0006中的asset_sub_sub_category字段保持一致 */
    private String assetSubSubCategory;

    /** 固收资产细分类,01:传统固定收益类投资资产,02:非标准固定收益类投资资产,03:其他固定收益类金融产品 */
    private String fixedIncomeSubCategory;

    /** 可计算现金流标识,0:否,1:是 */
    private String calculableCashflowFlag;

    /** 信用评级取值逻辑标识 */
    private String creditRatingLogicFlag;

    /** 行业统计标识,01:不考虑,02:银行,03:非银金融,04:wind中获取,05:房地产,06:建筑装饰 */
    private String industryStatisticsFlag;

    /** 单一资产统计标识,01:不考虑,02:考虑 */
    private String singleAssetStatisticsFlag;

    /** 五级分类资产统计标识,01:不考虑,02:债券类,03:存款类,04:固定收益类金融产品,05:未上市企业股权,06:股权金融产品,07:不动产金融产品 */
    private String fiveLevelStatisticsFlag;

    /** 利差久期资产统计标识,0:否,1:是 */
    private String spreadDurationStatisticsFlag;

    /** 创建时间 */
    private java.util.Date createTime;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    private java.util.Date updateTime;

    /** 更新者 */
    private String updateBy;

    /** 是否删除，0:否，1:是 */
    private Integer isDel;
}
