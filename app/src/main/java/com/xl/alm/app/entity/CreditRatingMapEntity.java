package com.xl.alm.app.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 信用评级映射表 Entity
 *
 * <AUTHOR> Assistant
 */
@Data
public class CreditRatingMapEntity {

    /** 主键 */
    private Long id;

    /** 账期,格式YYYYMM（如202406） */
    private String accountingPeriod;

    /** 原始信用评级,与TB0006中的credit_rating字段使用相同的字典数据 */
    private String creditRating;

    /** 信用评级表使用的评级,与TB0006中的credit_rating字段使用相同的字典数据 */
    private String creditRatingTableUsed;

    /** 折现曲线使用评级,与TB0006中的credit_rating字段使用相同的字典数据 */
    private String discountCurveRating;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 更新者 */
    private String updateBy;

    /** 是否删除，0:否，1:是 */
    private Integer isDel;
}
