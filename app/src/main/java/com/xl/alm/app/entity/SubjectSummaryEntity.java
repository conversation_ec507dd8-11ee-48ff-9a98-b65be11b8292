package com.xl.alm.app.entity;


import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 科目汇总账表实体类
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubjectSummaryEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 账期，格式：YYYYMM（如202406）
     */
    private String accountingPeriod;

    /**
     * 科目编码，会计科目编码
     */
    private String subjectCode;

    /**
     * 科目名称，会计科目名称
     */
    private String subjectName;

    /**
     * 期初余额，期初余额金额
     */
    private BigDecimal openingBalance;

    /**
     * 本期借方发生额，本期借方发生金额
     */
    private BigDecimal currentDebitAmount;

    /**
     * 本期贷方发生额，本期贷方发生金额
     */
    private BigDecimal currentCreditAmount;

    /**
     * 期末余额，期末余额金额
     */
    private BigDecimal endingBalance;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDel = 0;
}
