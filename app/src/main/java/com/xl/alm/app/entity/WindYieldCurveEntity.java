package com.xl.alm.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 万得收益率曲线表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
@TableName("t_base_wind_yield_curve")
public class WindYieldCurveEntity extends BaseEntity {
    @TableId
    private Long id;
    
    /**
     * 折现曲线名称
     */
    private String curveName;
    
    /**
     * 折现曲线标识
     */
    private String curveId;
    
    /**
     * 日期
     */
    private Date date;
    
    /**
     * 期限0
     */
    private BigDecimal term0;
    
    /**
     * 期限1
     */
    private BigDecimal term1;
    
    /**
     * 期限2
     */
    private BigDecimal term2;
    
    /**
     * 期限3
     */
    private BigDecimal term3;
    
    /**
     * 期限4
     */
    private BigDecimal term4;
    
    /**
     * 期限5
     */
    private BigDecimal term5;
    
    /**
     * 期限6
     */
    private BigDecimal term6;
    
    /**
     * 期限7
     */
    private BigDecimal term7;
    
    /**
     * 期限8
     */
    private BigDecimal term8;
    
    /**
     * 期限9
     */
    private BigDecimal term9;
    
    /**
     * 期限10
     */
    private BigDecimal term10;
    
    /**
     * 期限11
     */
    private BigDecimal term11;
    
    /**
     * 期限12
     */
    private BigDecimal term12;
    
    /**
     * 期限13
     */
    private BigDecimal term13;
    
    /**
     * 期限14
     */
    private BigDecimal term14;
    
    /**
     * 期限15
     */
    private BigDecimal term15;
    
    /**
     * 期限16
     */
    private BigDecimal term16;
    
    /**
     * 期限17
     */
    private BigDecimal term17;
    
    /**
     * 期限18
     */
    private BigDecimal term18;
    
    /**
     * 期限19
     */
    private BigDecimal term19;
    
    /**
     * 期限20
     */
    private BigDecimal term20;
    
    /**
     * 期限21
     */
    private BigDecimal term21;
    
    /**
     * 期限22
     */
    private BigDecimal term22;
    
    /**
     * 期限23
     */
    private BigDecimal term23;
    
    /**
     * 期限24
     */
    private BigDecimal term24;
    
    /**
     * 期限25
     */
    private BigDecimal term25;
    
    /**
     * 期限26
     */
    private BigDecimal term26;
    
    /**
     * 期限27
     */
    private BigDecimal term27;
    
    /**
     * 期限28
     */
    private BigDecimal term28;
    
    /**
     * 期限29
     */
    private BigDecimal term29;
    
    /**
     * 期限30
     */
    private BigDecimal term30;
    
    /**
     * 期限31
     */
    private BigDecimal term31;
    
    /**
     * 期限32
     */
    private BigDecimal term32;
    
    /**
     * 期限33
     */
    private BigDecimal term33;
    
    /**
     * 期限34
     */
    private BigDecimal term34;
    
    /**
     * 期限35
     */
    private BigDecimal term35;
    
    /**
     * 期限36
     */
    private BigDecimal term36;
    
    /**
     * 期限37
     */
    private BigDecimal term37;
    
    /**
     * 期限38
     */
    private BigDecimal term38;
    
    /**
     * 期限39
     */
    private BigDecimal term39;
    
    /**
     * 期限40
     */
    private BigDecimal term40;
    
    /**
     * 期限41
     */
    private BigDecimal term41;
    
    /**
     * 期限42
     */
    private BigDecimal term42;
    
    /**
     * 期限43
     */
    private BigDecimal term43;
    
    /**
     * 期限44
     */
    private BigDecimal term44;
    
    /**
     * 期限45
     */
    private BigDecimal term45;
    
    /**
     * 期限46
     */
    private BigDecimal term46;
    
    /**
     * 期限47
     */
    private BigDecimal term47;
    
    /**
     * 期限48
     */
    private BigDecimal term48;
    
    /**
     * 期限49
     */
    private BigDecimal term49;
    
    /**
     * 期限50
     */
    private BigDecimal term50;
    
    /**
     * 是否删除，0：否，1：是
     */
    private int isDel = 0;
}
