package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 偿付能力状况表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class SolvencyStatusEntity extends BaseEntity {
    
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 账期，格式：YYYYMM（如202503）
     */
    private String accountingPeriod;
    
    /**
     * 行次，表中行的序号
     */
    private String rowNumber;
    
    /**
     * 项目，偿付能力指标项目名称
     */
    private String itemName;
    
    /**
     * 期末数，期末余额
     */
    private BigDecimal endingBalance;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDel = 0;
}
