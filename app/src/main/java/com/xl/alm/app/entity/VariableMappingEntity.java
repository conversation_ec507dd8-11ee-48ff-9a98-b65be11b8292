package com.xl.alm.app.entity;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 变量映射表对象 t_cft_variable_mapping
 *
 * <AUTHOR> Assistant
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VariableMappingEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账期，格式：YYYYMM（如202412）
     */
    @Excel(name = "账期")
    @NotBlank(message = "账期不能为空")
    @Size(max = 6, message = "账期长度不能超过6个字符")
    private String accountingPeriod;

    /**
     * 变量列表，变量代码标识，现金流变量分类
     */
    @Excel(name = "变量列表")
    @NotBlank(message = "变量列表不能为空")
    @Size(max = 50, message = "变量列表长度不能超过50个字符")
    private String variableList;

    /**
     * 变量名称，变量中文名称，变量描述
     */
    @Excel(name = "变量名称")
    @NotBlank(message = "变量名称不能为空")
    @Size(max = 100, message = "变量名称长度不能超过100个字符")
    private String variableName;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Integer isDel;
}
