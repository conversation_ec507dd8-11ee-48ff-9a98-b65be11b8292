package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 账户名称映射表对象 t_ast_account_name_map
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class AccountNameMapEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 账期,格式YYYYMM（如202406）
     */
    private String accountingPeriod;

    /**
     * 原始账户名称,存储实际的原始文本,如传统、分红、万能、投连、资本补充债等
     */
    private String accountName;

    /**
     * 映射后的标准账户名称,01:传统账户,02:分红账户,03:万能账户,04:独立账户,05:资本补充债账户,06:普通账户
     */
    private String accountNameMapping;

    /**
     * 是否删除，0:否，1:是
     */
    private Integer isDel;
}
