<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="科目编码" prop="subjectCode">
        <el-select v-model="queryParams.subjectCode" placeholder="请选择科目编码" clearable style="width: 200px">
          <el-option label="130301-保户质押贷款-本金" value="130301" />
          <el-option label="130302-保户质押贷款-利息调整" value="130302" />
          <el-option label="100201-银行存款-活期存款" value="100201" />
          <el-option label="********-应收利息-银行存款-活期存款" value="********" />
        </el-select>
      </el-form-item>
      <el-form-item label="科目名称" prop="subjectName">
        <el-input
          v-model="queryParams.subjectName"
          placeholder="请输入科目名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="handleGenerate"
          v-hasPermi="['asm:non:investment:fund:utilization:generate']"
        >生成数据</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['asm:non:investment:fund:utilization:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="nonInvestmentFundUtilizationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="科目编码" align="center" prop="subjectCode" />
      <el-table-column label="科目名称" align="center" prop="subjectName" />
      <el-table-column label="期末余额" align="center" prop="endingBalance">
        <template slot-scope="scope">
          <span>{{ formatNumber(scope.row.endingBalance) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 生成数据对话框 -->
    <el-dialog title="生成非投资资金运用表数据" :visible.sync="generateOpen" width="400px" append-to-body>
      <el-form ref="generateForm" :model="generateForm" :rules="generateRules" label-width="100px">
        <el-form-item label="账期" prop="accountingPeriod">
          <el-input v-model="generateForm.accountingPeriod" placeholder="请输入账期，格式：YYYYMM" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGenerate">确 定</el-button>
        <el-button @click="generateOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNonInvestmentFundUtilization, generateNonInvestmentFundUtilization } from "@/api/asm/nonInvestmentFundUtilization";

export default {
  name: "NonInvestmentFundUtilization",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 非投资资金运用表表格数据
      nonInvestmentFundUtilizationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        subjectCode: null,
        subjectName: null,
      },
      // 生成数据对话框
      generateOpen: false,
      // 生成数据表单
      generateForm: {
        accountingPeriod: null
      },
      // 生成数据表单校验
      generateRules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式错误，应为YYYYMM格式", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询非投资资金运用表列表 */
    getList() {
      this.loading = true;
      listNonInvestmentFundUtilization(this.queryParams).then(response => {
        this.nonInvestmentFundUtilizationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 生成数据按钮操作 */
    handleGenerate() {
      this.generateForm.accountingPeriod = null;
      this.generateOpen = true;
    },
    /** 提交生成数据 */
    submitGenerate() {
      this.$refs["generateForm"].validate(valid => {
        if (valid) {
          this.$modal.confirm('是否确认生成账期"' + this.generateForm.accountingPeriod + '"的非投资资金运用表数据？').then(() => {
            return generateNonInvestmentFundUtilization(this.generateForm.accountingPeriod);
          }).then(() => {
            this.generateOpen = false;
            this.getList();
            this.$modal.msgSuccess("生成成功");
          }).catch(() => {});
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有非投资资金运用表数据项？').then(() => {
        this.download('asm/non/investment/fund/utilization/export', {
          ...this.queryParams
        }, `non_investment_fund_utilization_${new Date().getTime()}.xlsx`);
      });
    },
    /** 格式化数字 */
    formatNumber(value) {
      if (value == null || value === '') {
        return '0.00';
      }
      return parseFloat(value).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    }
  }
};
</script>
